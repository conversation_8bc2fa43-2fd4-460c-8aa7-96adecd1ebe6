<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductsPermissionToExchangeGoods;
?>
  <title><?php echo e(trans('admin.ExchangeGoodsSechdule')); ?></title>


     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Stores')); ?></a></li>
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.ExchangeGoodsSechdule')); ?> </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block">
                            <span class="js-get-date"></span></li>
                    </ol>


            <!-- Data -->
                    <div class="row hide-table">
                        <div class="col-xl-12">
                            <div id="panel-1" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                    <?php echo e(trans('admin.ExchangeGoodsSechdule')); ?>

                                    </h2>

                                    <div class="panel-toolbar">
                                        <button class="btn btn-primary btn-sm" data-toggle="dropdown">Table
                                            Style</button>
                                        <div
                                            class="dropdown-menu dropdown-menu-animated dropdown-menu-right position-absolute pos-top">
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-bordered" data-target="#dt-basic-example"> Bordered
                                                Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-sm"
                                                data-target="#dt-basic-example"> Smaller Table </button>
                                            <button class="dropdown-item" data-action="toggle" data-class="table-dark"
                                                data-target="#dt-basic-example"> Table Dark </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-hover" data-target="#dt-basic-example"> Table Hover
                                            </button>
                                            <button class="dropdown-item active" data-action="toggle"
                                                data-class="table-stripe" data-target="#dt-basic-example"> Table
                                                Stripped </button>
                                            <div class="dropdown-divider m-0"></div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    tbody color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-tbody-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="dropdown-multilevel dropdown-multilevel-left">
                                                <div class="dropdown-item">
                                                    thead color
                                                </div>
                                                <div class="dropdown-menu no-transition-delay">
                                                    <div class="js-thead-colors dropdown-multilevel dropdown-multilevel-left d-flex flex-wrap"
                                                        style="width: 15.9rem; padding: 0.5rem">
                                                        <a href="javascript:void(0);" data-bg="bg-primary-100"
                                                            class="btn d-inline-block bg-primary-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-200"
                                                            class="btn d-inline-block bg-primary-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-300"
                                                            class="btn d-inline-block bg-primary-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-400"
                                                            class="btn d-inline-block bg-primary-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-500"
                                                            class="btn d-inline-block bg-primary-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-600"
                                                            class="btn d-inline-block bg-primary-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-700"
                                                            class="btn d-inline-block bg-primary-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-800"
                                                            class="btn d-inline-block bg-primary-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-primary-900"
                                                            class="btn d-inline-block bg-primary-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-100"
                                                            class="btn d-inline-block bg-success-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-200"
                                                            class="btn d-inline-block bg-success-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-300"
                                                            class="btn d-inline-block bg-success-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-400"
                                                            class="btn d-inline-block bg-success-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-500"
                                                            class="btn d-inline-block bg-success-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-600"
                                                            class="btn d-inline-block bg-success-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-700"
                                                            class="btn d-inline-block bg-success-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-800"
                                                            class="btn d-inline-block bg-success-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-success-900"
                                                            class="btn d-inline-block bg-success-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-100"
                                                            class="btn d-inline-block bg-info-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-200"
                                                            class="btn d-inline-block bg-info-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-300"
                                                            class="btn d-inline-block bg-info-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-400"
                                                            class="btn d-inline-block bg-info-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-500"
                                                            class="btn d-inline-block bg-info-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-600"
                                                            class="btn d-inline-block bg-info-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-700"
                                                            class="btn d-inline-block bg-info-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-800"
                                                            class="btn d-inline-block bg-info-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-info-900"
                                                            class="btn d-inline-block bg-info-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-100"
                                                            class="btn d-inline-block bg-danger-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-200"
                                                            class="btn d-inline-block bg-danger-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-300"
                                                            class="btn d-inline-block bg-danger-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-400"
                                                            class="btn d-inline-block bg-danger-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-500"
                                                            class="btn d-inline-block bg-danger-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-600"
                                                            class="btn d-inline-block bg-danger-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-700"
                                                            class="btn d-inline-block bg-danger-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-800"
                                                            class="btn d-inline-block bg-danger-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-danger-900"
                                                            class="btn d-inline-block bg-danger-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-100"
                                                            class="btn d-inline-block bg-warning-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-200"
                                                            class="btn d-inline-block bg-warning-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-300"
                                                            class="btn d-inline-block bg-warning-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-400"
                                                            class="btn d-inline-block bg-warning-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-500"
                                                            class="btn d-inline-block bg-warning-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-600"
                                                            class="btn d-inline-block bg-warning-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-700"
                                                            class="btn d-inline-block bg-warning-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-800"
                                                            class="btn d-inline-block bg-warning-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-warning-900"
                                                            class="btn d-inline-block bg-warning-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-100"
                                                            class="btn d-inline-block bg-fusion-100 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-200"
                                                            class="btn d-inline-block bg-fusion-200 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-300"
                                                            class="btn d-inline-block bg-fusion-300 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-400"
                                                            class="btn d-inline-block bg-fusion-400 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-500"
                                                            class="btn d-inline-block bg-fusion-500 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-600"
                                                            class="btn d-inline-block bg-fusion-600 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-700"
                                                            class="btn d-inline-block bg-fusion-700 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-800"
                                                            class="btn d-inline-block bg-fusion-800 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg="bg-fusion-900"
                                                            class="btn d-inline-block bg-fusion-900 width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                        <a href="javascript:void(0);" data-bg=""
                                                            class="btn d-inline-block bg-white border width-2 height-2 p-0 rounded-0"
                                                            style="margin:1px"></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="panel-container show">
                              <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <div class="panel-content">

                                        <!-- datatable start -->
                                        <div style="overflow:auto;">
        <table id="dt-basic-example" class="table table-bordered table-hover table-striped ">
                                            <thead class="bg-highlight">
                                                <tr>
                                                    <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>

                                                    <th> <?php echo e(trans('admin.Account')); ?> </th>

                                                    <th> <?php echo e(trans('admin.Store')); ?> </th>

                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                    <th><?php echo e(trans('admin.Data')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($item->Code); ?></td>
                                                    <td><?php echo e($item->Date); ?></td>


                                                      <td>
                                              <?php if($item->Account()->first()): ?>
                                                  <?php echo e(app()->getLocale() == 'ar' ? $item->Account()->first()->Name : $item->Account()->first()->NameEn); ?>

                                              <?php else: ?>
                                                  <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                              <?php endif; ?>
                                                       </td>

                                                        <td>
                                                <?php if($item->Store()->first()): ?>
                                                    <?php echo e(app()->getLocale() == 'ar' ? $item->Store()->first()->Name : $item->Store()->first()->NameEn); ?>

                                                <?php else: ?>
                                                    <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                                <?php endif; ?>
                                                    </td>

                                                    <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-center-open<?php echo e($item->id); ?>">
                                                            <?php echo e(trans('admin.Details')); ?>

                                                        </button>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-default" data-toggle="modal" data-target="#show-data<?php echo e($item->id); ?>">
                                                           <?php echo e(trans('admin.Data')); ?>

                                                        </button>
                                                    </td>
                                                    <td class="text-center">


             <a href="<?php echo e(url('ExchangePrint/'.$item->id)); ?>" class="btn btn-default" >
                                                    <i class="fal fa-print"></i>
                                                    </a>
                                                          <?php if($item->Status  == 0): ?>

                                          <a href="<?php echo e(url('DeletePremissionExchange/'.$item->id)); ?>" class="btn btn-danger" >
                                                    <i class="fal fa-trash"></i>
                                                    </a>


                                       <a href="<?php echo e(url('EditPremissionExchange/'.$item->id)); ?>" class="btn btn-success" >
                                                    <i class="fal fa-edit"></i>
                                                    </a>



                                       <a href="<?php echo e(url('TransferToSalesExchange/'.$item->id)); ?>" class="btn btn-default" data-toggle="tooltip" data-placement="top" title="" data-original-title="<?php echo e(trans('admin.Trans_To_Sales_Bill')); ?>"><i class="fal fa-repeat"></i></a>

                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                   <th><?php echo e(trans('admin.Code')); ?></th>
                                                    <th><?php echo e(trans('admin.Date')); ?></th>

                                                    <th> <?php echo e(trans('admin.Account')); ?> </th>

                                                    <th> <?php echo e(trans('admin.Store')); ?> </th>

                                                    <th><?php echo e(trans('admin.Details')); ?></th>
                                                     <th><?php echo e(trans('admin.Data')); ?></th>
                                                    <th><?php echo e(trans('admin.Actions')); ?> </th>
                                                </tr>
                                            </tfoot>

                                        </table>

                                        <?php echo e($items->Links()); ?>

                                        </div>
                                        <!-- datatable end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

  <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>



                   <!-- Modal Show data -->
    <div class="modal fade" id="show-data<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                              <?php echo e(trans('admin.Data')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div style="overflow:auto;">
        <table  class="table table-bordered table-hover table-striped " style="width:100%;">
                                            <thead class="bg-highlight">
                                                <tr>

                                                    <th> <?php echo e(trans('admin.Draw')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?> </th>


                                                    <th> <?php echo e(trans('admin.Coin')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?> </th>
                                                    <th> <?php echo e(trans('admin.User')); ?> </th>

                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>

                                                      <td><?php echo e($item->Draw); ?></td>

                                                        <td><?php echo e($item->Note); ?></td>


                                                        <td>
                                                <?php if($item->Coin()->first()): ?>
                                                    <?php echo e(app()->getLocale() == 'ar' ? $item->Coin()->first()->Arabic_Name : $item->Coin()->first()->English_Name); ?>

                                                <?php else: ?>
                                                    <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                                <?php endif; ?>
                                                    </td>
                                                            <td>
                                                     <?php if(!empty($item->Cost_Center) && $item->Cost_Center()->first()): ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? $item->Cost_Center()->first()->Arabic_Name : $item->Cost_Center()->first()->English_Name); ?>

                                                    <?php else: ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                                    <?php endif; ?>
                                                    </td>
                                                    <td>
                                                    <?php if($item->User()->first()): ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? $item->User()->first()->name : $item->User()->first()->nameEn); ?>

                                                    <?php else: ?>
                                                        <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                                    <?php endif; ?>
                                                    </td>


                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                            </tbody>
                                            <tfoot>
                                                <tr>

                                                    <th> <?php echo e(trans('admin.Draw')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Notes')); ?> </th>

                                                    <th> <?php echo e(trans('admin.Coin')); ?> </th>
                                                    <th> <?php echo e(trans('admin.Cost_Center')); ?> </th>
                                                    <th> <?php echo e(trans('admin.User')); ?> </th>

                                                </tr>
                                            </tfoot>

                                        </table>

                                        <?php echo e($items->Links()); ?>

                                        </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>

                        </div>
                    </div>
                </div>
            </div>


                        <!-- Modal Details -->
    <div class="modal fade" id="default-example-modal-center-open<?php echo e($item->id); ?>" tabindex="-1" role="dialog"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">
                               <?php echo e(trans('admin.Details')); ?>

                            </h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="fal fa-times"></i></span>
                            </button>
                        </div>
                        <div class="modal-body">


                <?php
                $details=ProductsPermissionToExchangeGoods::where('Exchange',$item->id)->get();
                ?>
                        <div class="mt-3">
                            <div style="overflow:auto">
                            <table id=""
                            class="table table-bordered table-hover table-striped " style="width:180%">
                            <thead>
                                <tr>
                                    <th><?php echo e(trans('admin.Code')); ?> </th>
                                    <th><?php echo e(trans('admin.Name')); ?> </th>
                                     <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.AvQty')); ?> </th>
                                    <th><?php echo e(trans('admin.Qty')); ?> </th>
                                    <th><?php echo e(trans('admin.Store')); ?> </th>
                                    <th><?php echo e(trans('admin.Exp_Date')); ?> </th>
                                </tr>
                            </thead>
                            <tbody id="">
                                <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($detail->Product_Code); ?></td>
                                    <td>
                                        <?php if($detail->Product()->first()): ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? $detail->Product()->first()->P_Ar_Name : $detail->Product()->first()->P_En_Name); ?>

                                        <?php else: ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                        <?php endif; ?>
                                        <?php if(!empty($detail->V1) && $detail->V1()->first()): ?>
                                            ( <?php echo e(app()->getLocale() == 'ar' ? $detail->V1()->first()->Name : $detail->V1()->first()->NameEn); ?> )
                                        <?php endif; ?>
                                        <?php if(!empty($detail->V2) && $detail->V2()->first()): ?>
                                            ( <?php echo e(app()->getLocale() == 'ar' ? $detail->V2()->first()->Name : $detail->V2()->first()->NameEn); ?> )
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($detail->Unit()->first()): ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? $detail->Unit()->first()->Name : $detail->Unit()->first()->NameEn); ?>

                                        <?php else: ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo e($detail->AvQty); ?></td>
                                    <td><?php echo e($detail->Qty); ?></td>




                                    <td>
                                        <?php if($detail->Store()->first()): ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? $detail->Store()->first()->Name : $detail->Store()->first()->NameEn); ?>

                                        <?php else: ?>
                                            <?php echo e(app()->getLocale() == 'ar' ? 'غير محدد' : 'Not Specified'); ?>

                                        <?php endif; ?>
                                    </td>

                                    <td><?php echo e($detail->Exp_Date); ?></td>

                                </tr>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                  <th><?php echo e(trans('admin.Code')); ?> </th>
                                    <th><?php echo e(trans('admin.Name')); ?> </th>
                                     <th><?php echo e(trans('admin.Unit')); ?> </th>
                                    <th><?php echo e(trans('admin.AvQty')); ?> </th>
                                    <th><?php echo e(trans('admin.Qty')); ?> </th>

                                    <th><?php echo e(trans('admin.Store')); ?> </th>

                                    <th><?php echo e(trans('admin.Exp_Date')); ?> </th>
                                </tr>
                            </tfoot>
                        </table>
                        </div>
                        <table class="table table-bordered table-hover table-striped w-100 mt-4">
                            <tbody>
                                <tr>
                                    <td><?php echo e(trans('admin.Product_Numbers')); ?></td>
                                    <td><?php echo e($item->Product_Numbers); ?></td>

                                    <td><?php echo e(trans('admin.Total_Qty')); ?></td>
                                    <td><?php echo e($item->Total_Qty); ?></td>




                                </tr>
                            </tbody>
                        </table>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>

                        </div>
                    </div>
                </div>
            </div>

            </div>

<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>



<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">


    <style>
        th{
            width:135px!important;
        }
    </style>

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search '  + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    // responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>


<script>
    var autoSave = $('#autoSave');
    var interval;
    var timer = function()
    {
        interval = setInterval(function()
        {
            //start slide...
            if (autoSave.prop('checked'))
                saveToLocal();

            clearInterval(interval);
        }, 3000);
    };

    //save
    var saveToLocal = function()
    {
        localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
        console.log("saved");
    }

    //delete
    var removeFromLocal = function()
    {
        localStorage.removeItem("summernoteData");
        $('#saveToLocal').summernote('reset');
    }

    $(document).ready(function()
    {
        //init default
        $('.js-summernote').summernote(
        {
            height: 200,
            tabsize: 2,
            placeholder: "Type here...",
            dialogsFade: true,
            toolbar: [
                ['style', ['style']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']]
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks:
            {
                //restore from localStorage
                onInit: function(e)
                {
                    $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                },
                onChange: function(contents, $editable)
                {
                    clearInterval(interval);
                    timer();
                }
            }
        });

        //load emojis
        $.ajax(
        {
            url: 'https://api.github.com/emojis',
            async: false
        }).then(function(data)
        {
            window.emojis = Object.keys(data);
            window.emojiUrls = data;
        });

        //init emoji example
        $(".js-hint2emoji").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: 'type starting with : and any alphabet',
            hint:
            {
                match: /:([\-+\w]+)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(emojis, function(item)
                    {
                        return item.indexOf(keyword) === 0;
                    }));
                },
                template: function(item)
                {
                    var content = emojiUrls[item];
                    return '<img src="' + content + '" width="20" /> :' + item + ':';
                },
                content: function(item)
                {
                    var url = emojiUrls[item];
                    if (url)
                    {
                        return $('<img />').attr('src', url).css('width', 20)[0];
                    }
                    return '';
                }
            }
        });

        //init mentions example
        $(".js-hint2mention").summernote(
        {
            height: 100,
            toolbar: false,
            placeholder: "type starting with @",
            hint:
            {
                mentions: ['jayden', 'sam', 'alvin', 'david'],
                match: /\B@(\w*)$/,
                search: function(keyword, callback)
                {
                    callback($.grep(this.mentions, function(item)
                    {
                        return item.indexOf(keyword) == 0;
                    }));
                },
                content: function(item)
                {
                    return '@' + item;
                }
            }
        });

    });

</script>
<script type="text/javascript">


    $(".show-table").click(function(){
        $(".hide-table").show();
    });

</script>

	<style>
	@media  print {
		body * {
			visibility: hidden;
		}
		.modal-content * {
			visibility: visible;
			overflow: visible;
		}
		.main-page * {
			display: none;
		}
		.modal {
			position: absolute;
			left: 0;
			top: -220px;
			margin: 0;
			padding: 0;
			min-height: 550px;
			visibility: visible;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}
		.modal-dialog {
			visibility: visible !important;
			overflow: visible !important; /* Remove scrollbar for printing. */
		}

        .page-content{
            display:none;
        }
        @page  {

		size: a4;

    }

	}
	</style>

    <!-- Search Selecet -->
<script>
    $(document).ready(function () {
        $(function () {
            $(".select2").select2();

            $(".select2-placeholder-multiple").select2({
                placeholder: "Select State",
            });
            $(".js-hide-search").select2({
                minimumResultsForSearch: 1 / 0,
            });
            $(".js-max-length").select2({
                maximumSelectionLength: 2,
                placeholder: "Select maximum 2 items",
            });
            $(".select2-placeholder").select2({
                placeholder: "Select a state",
                allowClear: true,
            });

            $(".js-select2-icons").select2({
                minimumResultsForSearch: 1 / 0,
                templateResult: icon,
                templateSelection: icon,
                escapeMarkup: function (elm) {
                    return elm;
                },
            });

            function icon(elm) {
                elm.element;
                return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text;
            }

            $("#Emp").select2({
                placeholder: "select...",
                ajax: {
                    type: "GET",
                    dataType: "json",
                    url: "AllEmps",
                    processResults: function (data) {
                        return {
                            results: $.map(data, function (obj, index) {
                                return { id: index, text: obj };
                            }),
                        };

                        console.log(data);
                    },
                    data: function (params) {
                        var query = {
                            search: params.term,
                        };
                        if (params.term == "*") query.items = [];
                        return { json: JSON.stringify(query) };
                    },
                },
            });

            $("#Emp").on("select2:select", function (e) {
                console.log("select done", e.params.data);
            });
        });
    });
</script>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Stores/ExchangeGoodsSechdule.blade.php ENDPATH**/ ?>