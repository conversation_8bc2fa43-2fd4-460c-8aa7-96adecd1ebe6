<?php $__env->startSection('content'); ?>
   <?php
use App\Models\StoresDefaultData;
use App\Models\DefaultDataShowHide;
$Def=StoresDefaultData::orderBy('id','desc')->first();
$show=DefaultDataShowHide::orderBy('id','desc')->first();
?>
  <title><?php echo e(trans('admin.Consists')); ?></title>

   <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Stores')); ?> </a></li>
                        <li class="breadcrumb-item active"><?php echo e(trans('admin.Consists')); ?>   </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
    <form action="<?php echo e(url('AddConsists')); ?>" method="post">
        <?php echo csrf_field(); ?>

         <?php echo view('honeypot::honeypotFormFields'); ?>
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">
                                    <h2>
                                        <span class="fw-300"><i> <?php echo e(trans('admin.Consists')); ?>  </i></span>
                                    </h2>
                                </div>
                                <div class="panel-container show">
                                  <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>       
                                    <div class="panel-content">
                                <form action="">
                                    <div class="form-row">
                                        <div class="form-group col-lg-2">
                                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Code')); ?>  </label>
                             <input type="text"  value="<?php echo e($Code); ?>" class="form-control " disabled>
                             <input type="hidden" name="Code"  value="<?php echo e($Code); ?>">
                                        </div>
                                        <div class="form-group col-lg-2">
                               <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Date')); ?></label>
                             <input type="date" name="Date" value="<?php echo e(date('Y-m-d')); ?>" class="form-control" required>
                                        </div>
                                        <div class="form-group col-lg-2">
                        <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
             <select class="select2 form-control w-100" id="store" name="Store" required>
                                                <option value=""> <?php echo e(trans('admin.Store')); ?></option>
                                            <?php $__currentLoopData = $Stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
             <option value="<?php echo e($store->id); ?>" <?php if($Def->Store == $store->id): ?> selected <?php endif; ?> >
                 
                  <?php echo e(app()->getLocale() == 'ar' ?$store->Name :$store->NameEn); ?>

                 </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                                            </select>
                                        </div>
                                        <?php if($show->Coin == 1): ?>
                             <div class="form-group col-lg-2">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                    <option value="<?php echo e($coin->id); ?>"  <?php if($Def->Coin == $coin->id): ?> selected <?php endif; ?> >
                            <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>                                                
                                                
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                                            </select>
                                        </div>
                                        <?php else: ?>
                                  <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                    <option value="<?php echo e($coin->id); ?>"  <?php if($Def->Coin == $coin->id): ?> selected <?php endif; ?> >       <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                                            </select>
                                        </div>        
                                        <?php endif; ?>
                                        
                                               <?php if($show->Draw == 1): ?>
                                        <div class="form-group col-lg-2">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                        <input type="number" step="any" value="1" name="Draw" class="form-control" required>
                                        </div>     
                                        <?php else: ?>
                                   <div class="form-group col-lg-2" style="display: none">
                                            <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                        <input type="number" step="any" value="1" name="Draw" class="form-control" required>
                                        </div>         
                                        <?php endif; ?>
                                        
                                                                               <div class="form-group col-lg-4">
                        <label class="form-label" for=""><?php echo e(trans('admin.Price')); ?></label>
             <select class="select2 form-control w-100" id="pricee">
                                                <option value="Cost"> <?php echo e(trans('admin.Cost')); ?></option>
                                                <option value="Sale"> <?php echo e(trans('admin.Sale')); ?></option>
                
                                            </select>
                                        </div>


                                        
                                                       <div class="form-group col-lg-2">
                                 <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Account')); ?></label>
                <select  class="js-data-example-ajax form-control w-100" name="Account" id="Account" required>

                                                    </select>
                                        </div>

                                        <div class="form-group col-lg-12">
                                            <div class="input-items"style="position:relative;" >
                   <input type="text" id="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                                     <?php if(app()->getLocale() == 'ar' ): ?>
                     <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;left:3px;"></i>
                      <?php else: ?>
                            <i class="fal fa-barcode-alt" style="position:absolute ;top:5px;right: 0;left:auto;"></i>
                      <?php endif; ?>
                                            </div>
                                         </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class="col-xl-12">
                        <div id="panel-1" class="panel">
                          
                            <div class="panel-container show">
                                <div class="panel-content">
                                 
                                    <!-- datatable start -->    
                                    <div id="mobile-overflow">
                    <table id="dt-basic-example" class="table table-bordered table-hover table-striped w-100 mobile-width th-width table-color1">
                                        <thead>
                                            <tr>
                                                <th> <?php echo e(trans('admin.Name')); ?></th>
                                                <th> <?php echo e(trans('admin.Unit')); ?></th>
                                                <th> <?php echo e(trans('admin.Code')); ?></th>
                                                <th> <?php echo e(trans('admin.Qty')); ?></th>
                                                <th> <?php echo e(trans('admin.Consist')); ?></th>
                                                <th> <?php echo e(trans('admin.Price')); ?></th>    
                                                <th> <?php echo e(trans('admin.Total')); ?></th>
                                                <th> <?php echo e(trans('admin.Actions')); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody class="Data" id="Data">

                                        </tbody>
                                    </table>
                                    <!-- datatable end -->
                                    <div class="row">
                                        <div class="col-md-12">
                              <table id="dt" class="table table-bordered table-hover table-striped w-100 table-color2">
                                            <thead>
                                                <tr>
                                                <th> <?php echo e(trans('admin.Name')); ?></th>
                                                <th> <?php echo e(trans('admin.Unit')); ?></th>
                                                <th> <?php echo e(trans('admin.Code')); ?></th>
                                                <th> <?php echo e(trans('admin.Qty')); ?></th>
                                                <th> <?php echo e(trans('admin.Consist')); ?></th>
                                                <th> <?php echo e(trans('admin.Price')); ?></th>    
                                                <th> <?php echo e(trans('admin.Total')); ?></th>
                                                <th> <?php echo e(trans('admin.Actions')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody id="data-dt">
                                                
                                            </tbody>
                                        </table>
                                        </div>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group col-lg-4">
                                 <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Product_Numbers')); ?> </label>
                      <input type="text" id="Product_Numbers" disabled  class="form-control">
                      <input type="hidden" id="Product_NumbersHide" name="Products_Number">
                                        </div>
                                        <div class="form-group col-lg-4">
                         <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Total_Qty')); ?> </label>
                                           <input type="text" id="Total_Qty" disabled  class="form-control">
                                            <input type="hidden" id="Total_QtyHide" name="Total_Qty">
                                        </div>
                 

                                        <div class="form-group col-lg-3">
                       <label class="form-label" for="simpleinput">  <?php echo e(trans('admin.Total_Price')); ?></label>
                                   <input type="text" id="Total_Price" disabled  class="form-control">
                                            <input type="hidden" id="Total_PriceHide" name="Total_Price">
                                        </div>
         
                                    </div>

                                    <div class="buttons mt-3">
   <button type="submit" id="Submit" class="btn btn-primary" style="display: none"> <i class="fal fa-folder"></i> <?php echo e(trans('admin.Save')); ?> </button>
                                      </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    </div>
        </form> 
                </main>

<?php if(app()->getLocale() == 'ar' ): ?> 
<input type="hidden" id="LANG" value="ar">
<?php else: ?>
<input type="hidden" id="LANG" value="en">
<?php endif; ?>

<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
  <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
      <script>
        var autoSave = $('#autoSave');
        var interval;
        var timer = function()
        {
            interval = setInterval(function()
            {
                //start slide...
                if (autoSave.prop('checked'))
                    saveToLocal();

                clearInterval(interval);
            }, 3000);
        };

        //save
        var saveToLocal = function()
        {
            localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
            console.log("saved");
        }

        //delete 
        var removeFromLocal = function()
        {
            localStorage.removeItem("summernoteData");
            $('#saveToLocal').summernote('reset');
        }

        $(document).ready(function()
        {
            //init default
            $('.js-summernote').summernote(
            {
                height: 200,
                tabsize: 2,
                placeholder: "Type here...",
                dialogsFade: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['font', ['bold', 'italic', 'underline', 'clear']],
                    ['fontsize', ['fontsize']],
                    ['fontname', ['fontname']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'video']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks:
                {
                    //restore from localStorage
                    onInit: function(e)
                    {
                        $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
                    },
                    onChange: function(contents, $editable)
                    {
                        clearInterval(interval);
                        timer();
                    }
                }
            });

            //load emojis
            $.ajax(
            {
                url: 'https://api.github.com/emojis',
                async: false
            }).then(function(data)
            {
                window.emojis = Object.keys(data);
                window.emojiUrls = data;
            });

            //init emoji example
            $(".js-hint2emoji").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: 'type starting with : and any alphabet',
                hint:
                {
                    match: /:([\-+\w]+)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(emojis, function(item)
                        {
                            return item.indexOf(keyword) === 0;
                        }));
                    },
                    template: function(item)
                    {
                        var content = emojiUrls[item];
                        return '<img src="' + content + '" width="20" /> :' + item + ':';
                    },
                    content: function(item)
                    {
                        var url = emojiUrls[item];
                        if (url)
                        {
                            return $('<img />').attr('src', url).css('width', 20)[0];
                        }
                        return '';
                    }
                }
            });

            //init mentions example
            $(".js-hint2mention").summernote(
            {
                height: 100,
                toolbar: false,
                placeholder: "type starting with @",
                hint:
                {
                    mentions: ['jayden', 'sam', 'alvin', 'david'],
                    match: /\B@(\w*)$/,
                    search: function(keyword, callback)
                    {
                        callback($.grep(this.mentions, function(item)
                        {
                            return item.indexOf(keyword) == 0;
                        }));
                    },
                    content: function(item)
                    {
                        return '@' + item;
                    }
                }
            });

        });

    </script>

<!-- Search Selecet -->
 <script>
        $(document).ready(function()
        {
            $(function()
            {
                $('.select2').select2();

                $(".select2-placeholder-multiple").select2(
                {
                    placeholder: "Select State"
                });
                $(".js-hide-search").select2(
                {
                    minimumResultsForSearch: 1 / 0
                });
                $(".js-max-length").select2(
                {
                    maximumSelectionLength: 2,
                    placeholder: "Select maximum 2 items"
                });
                $(".select2-placeholder").select2(
                {
                    placeholder: "Select a state",
                    allowClear: true
                });

                $(".js-select2-icons").select2(
                {
                    minimumResultsForSearch: 1 / 0,
                    templateResult: icon,
                    templateSelection: icon,
                    escapeMarkup: function(elm)
                    {
                        return elm
                    }
                });

                function icon(elm)
                {
                    elm.element;
                    return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
                }

 
  $('#Account').select2({
    placeholder: "select...",
    ajax: {
        type: "GET",
        dataType: 'json',
        url: 'AllSubAccounts',
        processResults: function (data) {
          return {
            results: $.map(data, function(obj, index) {
              return { id: index, text: obj };
            })
          };
            
            	console.log(data);
            
        },
        data: function (params) {  
   
            
                   var query = {
                            search: params.term,
                        };
                                
            
              $.ajax({
                              url: 'AllSubAccountsJ/'+params.term,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },

                              success:function(data) {
                                          $('#Account').empty();  
                                  $.each(data, function(key, value){
   
                         $('#Account').append('<option value="'+ key +'">' + value + '</option>');
                   
                                  });
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
            
            
            
        }
    }
  });

    
$('#Account').on('select2:select', function (e) {
	console.log("select done", e.params.data);
});
           
                
  
            });
        });



    </script>

<!--  Filter Product -->
<script>
   $(document).ready(function(){
   
    fetch_customer_data();
   
    function fetch_customer_data(search = '',store = '',pricee='')
    {
     $.ajax({
      url:'ConsistFilter',
      method:'GET',
      data:{search:search,store:store,pricee:pricee},
      dataType:'json',
      success:function(data)
      {
       $('.Data').html(data.table_data);
             $("#Data").show();
      }
     })
    }
    
  $(document).on('keyup', '#search', function(){
     var search = $(this).val();     
     var store = $('#store').val();     
     var pricee = $('#pricee').val();     
     fetch_customer_data(search,store,pricee);
    });
       
      $(document).on('change', '#store', function(){
     var store = $(this).val();     
     var search = $('#search').val();        
     var pricee = $('#pricee').val();     
     fetch_customer_data(search,store,pricee);
    });   
       
       
             $(document).on('change', '#pricee', function(){
     var pricee = $(this).val();     
     var search = $('#search').val();        
     var store = $('#store').val();     
     fetch_customer_data(search,store,pricee);
    });   
       
      
       
   });
</script>

<!-- Unit Code and Name  V-->
<script>
    function  UnitCodeStart(x){
    
    var countryId = $('#UnitStart'+x).val();
    var Pro = $('#Product'+x).val();
    var code = $('#CodeStart'+x).val();
  var store = $('#store').val();   
       
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameCodeInventoryFilter/'+countryId+'/'+Pro+'/'+code+'/'+store ,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
                                      
                      
                        $('#UnitStartName'+x).val(data.name); 
                        $('#Price'+x).val(parseFloat(data.price).toFixed(2)); 
                        $('#Qty'+x).val(parseFloat(data.qty).toFixed(2)); 
                                      

                                  });
                                    
       var Price =$('#Price'+x).val(); 
     var Qty =$('#Qty'+x).val(); 
     var Inventory =$('#Inventory'+x).val(); 
   
 var result = parseFloat(Qty) -   parseFloat(Inventory) ;  
 
     if(parseFloat(result) < 0){
         
          $('#Dificit'+x).val(0); 
          $('#Excess'+x).val(result); 
         
    var res = parseFloat(-result) * parseFloat(Price) ;  
         
          $('#TotalDificit'+x).val(0); 
          $('#TotalExcess'+x).val(res); 

     }else{
         $('#Dificit'+x).val(result); 
     $('#Excess'+x).val(0);  

         
       var res = parseFloat(result) * parseFloat(Price) ;  
         
         
          $('#TotalDificit'+x).val(res); 
          $('#TotalExcess'+x).val(0);      
         
         
     }
    
     
     
     var Dificit = $('#Dificit'+x).val();
var Excess = $('#Excess'+x).val();
var TotalDificit = $('#TotalDificit'+x).val();
var TotalExcess = $('#TotalExcess'+x).val();
var UnitID = $('#UnitStart'+x).val();
     
    
       if(Dificit == ''  || Excess == ''  ||  TotalDificit == '' ||  TotalExcess == '' || UnitID == '' || Inventory == '' ){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(Dificit != ''  && Excess != ''  &&  TotalDificit != '' && TotalExcess != '' && UnitID != '' && Inventory != ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "block";           
     }  
            
         
                                  
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }

}   
</script>    

<!-- Unit Code and Name  -->
<script>
    function  UnitCodeStartt(x){
    
    var countryId = $('#UnitStart'+x).val();
    var Pro = $('#Product'+x).val();
    var code = $('#CodeStart'+x).val();
    var store = $('#store').val();     
                      if(countryId) {
                          $.ajax({
                              url: 'UnitNameCodeInventoryFilter/'+countryId+'/'+Pro+'/'+code+'/'+store ,
                              type:"GET",
                              dataType:"json",
                              beforeSend: function(){
                                  $('#loader').css("visibility", "visible");
                              },
   
                              success:function(data) {
                                  $.each(data, function(key, value){
                                      
                        $('#CodeStart'+x).val(data.code); 
                        $('#UnitStartName'+x).val(data.name); 
                        $('#Price'+x).val(parseFloat(data.price).toFixed(2)); 
                        $('#Qty'+x).val(parseFloat(data.qty).toFixed(2)); 
                                      

                                  });
                                    
       var Price =$('#Price'+x).val(); 
     var Qty =$('#Qty'+x).val(); 
     var Inventory =$('#Inventory'+x).val(); 
   
 var result = parseFloat(Qty) -   parseFloat(Inventory) ;  
 
     if(parseFloat(result) < 0){
         
          $('#Dificit'+x).val(0); 
          $('#Excess'+x).val(result); 
         
    var res = parseFloat(-result) * parseFloat(Price) ;  
         
          $('#TotalDificit'+x).val(0); 
          $('#TotalExcess'+x).val(res); 

     }else{
         $('#Dificit'+x).val(result); 
     $('#Excess'+x).val(0);  

         
       var res = parseFloat(result) * parseFloat(Price) ;  
         
         
          $('#TotalDificit'+x).val(res); 
          $('#TotalExcess'+x).val(0);      
         
         
     }
    
     
     
     var Dificit = $('#Dificit'+x).val();
var Excess = $('#Excess'+x).val();
var TotalDificit = $('#TotalDificit'+x).val();
var TotalExcess = $('#TotalExcess'+x).val();
var UnitID = $('#UnitStart'+x).val();
     
    
       if(Dificit == ''  || Excess == ''  ||  TotalDificit == '' ||  TotalExcess == '' || UnitID == '' || Inventory == '' ){
         
        document.getElementById("AddBtnPur"+x).style.display = "none";           
     }

     
       if(Dificit != ''  && Excess != ''  &&  TotalDificit != '' && TotalExcess != '' && UnitID != '' && Inventory != ''){
         
        document.getElementById("AddBtnPur"+x).style.display = "block";           
     }  
            
         
                                  
                                  
                                  
                              },
                              complete: function(){
                                  $('#loader').css("visibility", "hidden");
                              }
                          });
                      } else {
   
                          $('select[name="state"]').empty();
                      }

}   
</script>    

<!-- Consist  -->
<script>
 function Inventory(r){
     
     var Price =$('#Price'+r).val(); 
     var Consist =$('#Cosist'+r).val(); 
     var Qty =$('#Qty'+r).val(); 
     
     
     var result= parseFloat(Consist) * parseFloat(Price);
     
     $('#Total'+r).val(parseFloat(result));
     
     var Total =$('#Total'+r).val(); 


    
       if(Price == ''  || Consist == ''  ||  Total == ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "none";           
     }

     
       if(Price != ''  && Consist != ''  &&  Total != ''){
         
        document.getElementById("AddBtnPur"+r).style.display = "block";           
     }  

     if(parseFloat(Consist) > parseFloat(Qty)){
         
       document.getElementById("AddBtnPur"+r).style.display = "none";     
     }else if(parseFloat(Qty) == 0){
         
       document.getElementById("AddBtnPur"+r).style.display = "none";     
     }  
     
       
     
 }
</script>

  <!-- Add Products -->
<script>
   function Fun(r) { 
       
             var P_Ar_Name = $("#P_Ar_Name"+r).val();
             var P_En_Name= $("#P_En_Name"+r).val();
             var Product = $("#Product"+r).val();
             var UnitID = $("#UnitStart"+r).val();
             var UnitName = $("#UnitStartName"+r).val();
             var Qty = $("#Qty"+r).val();
             var Barcode = $("#CodeStart"+r).val();
             var Price = $("#Price"+r).val();
             var Consist = $("#Cosist"+r).val();
             var Total = $("#Total"+r).val();
             var V_Name = $("#V_Name"+r).val();
             var VOne = $("#VOne"+r).val();
             var VV_Name = $("#VV_Name"+r).val();
             var VTwo = $("#VTwo"+r).val();
             var Viro = '';
             var VViro = '';

       if( V_Name != ''){
           
          Viro= '(' + V_Name +')';
       }
  
       if( VV_Name != ''){
           
         VViro= '(' + VV_Name +')'; 
       }
       
       
               var LANG = $("#LANG").val();
   if(LANG == 'ar' ){ 
          var Nemo = P_Ar_Name ;
          }else{
             var Nemo = P_En_Name ;   
          } 

             document.getElementById("AddBtnPur"+r).style.display = "none";
             document.getElementById("Row"+r).style.display = "none";

             var markup = "<tr><td><input type='hidden' name='P_Ar_Name[]' value='"+P_Ar_Name+"'><input type='hidden' name='P_En_Name[]' value='"+P_En_Name+"'>" + Nemo + ""+ Viro +" "+ VViro +"</td><td><input type='hidden' name='Unit[]' value='"+UnitID+"'>" + UnitName + "</td><td><input type='hidden' name='P_Code[]' value='"+Barcode+"'>" + Barcode + "</td><td><input type='hidden' name='Qty[]' value='"+Qty+"'>" + Qty + "</td><td><input class='TotQty' type='hidden' name='Consist[]' value='"+Consist+"'>" + Consist + "</td><td><input type='hidden' name='Price[]' value='"+Price+"'>" + Price + "</td><td><input type='hidden' class='Tot' name='Total[]' value='"+Total+"'>" + Total + "</td><td><button id='DelAssem' type='button' class='btn btn-default'><i class='fal fa-trash'></i></button><input id='Product_IDInp"+r+"' type='hidden' name='Product[]' value='"+Product+"'><input type='hidden' name='VOne[]' value='"+VOne+"'><input type='hidden' name='VTwo[]' value='"+VTwo+"'><input type='hidden' name='V_Name[]' value='"+V_Name+"'><input type='hidden' name='VV_Name[]' value='"+VV_Name+"'></td></tr>";
   


            var  Product_IDInp =$("#Product_IDInp"+r).val();
       
         if(Product != Product_IDInp){
             $("#data-dt").append(markup);
                $("#Data").hide();
         }
       
       
      $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };
   
  var rowctr = $('#dt').rowCount();     
                
    var sumD = 0;        
$('.TotQty').each(function(){
  sumD += parseFloat($(this).val());
});   
            
    var sumE = 0;        
$('.Tot').each(function(){
  sumE += parseFloat($(this).val());
}); 
       
 
 $('#Product_Numbers').val(parseFloat(rowctr));
 $('#Product_NumbersHide').val(parseFloat(rowctr));
 $('#Total_Qty').val(parseFloat(sumD));
 $('#Total_QtyHide').val(parseFloat(sumD));
 $('#Total_Price').val(parseFloat(sumE));
 $('#Total_PriceHide').val(parseFloat(sumE));


      
       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
       
       

        $('#data-dt').on('click', '#DelAssem', function(e){
                $(this).closest('tr').remove(); 
            
       $.fn.rowCount = function() {
     return $('tr', $(this).find('tbody')).length;
   };
   
  var rowctr = $('#dt').rowCount();     
                
    var sumD = 0;        
$('.TotQty').each(function(){
  sumD += parseFloat($(this).val());
});   
            
    var sumE = 0;        
$('.Tot').each(function(){
  sumE += parseFloat($(this).val());
}); 
       
 
 $('#Product_Numbers').val(parseFloat(rowctr));
 $('#Product_NumbersHide').val(parseFloat(rowctr));
 $('#Total_Qty').val(parseFloat(sumD));
 $('#Total_QtyHide').val(parseFloat(sumD));
 $('#Total_Price').val(parseFloat(sumE));
 $('#Total_PriceHide').val(parseFloat(sumE));


      
       if(rowctr == 0){
        document.getElementById("Submit").style.display = "none";    
           
       }else{
           
        document.getElementById("Submit").style.display = "block";    
       }
       
       
 
            
                    })  
       
       
     }    
</script> 

<script>
  $(document).on('change', '#store', function(){

      $("#dt").find("tr:gt(0)").remove();
      
      
    });
</script>

<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\erp\resources\views/admin/Stores/Consists.blade.php ENDPATH**/ ?>